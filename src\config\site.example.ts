// Portfolio Configuration Template
// Copy this file to site.ts and customize with your information

export const siteConfig = {
  // 👤 Personal Information - CHANGE THESE
  name: "Your Name",
  email: "<EMAIL>",
  jobTitle: "Senior Backend Engineer",
  location: "Phnom Penh, Cambodia",
  domain: "https://yourname.dev",
  
  // 📝 Professional Bio - CUSTOMIZE THIS
  bio: "Backend engineer with 5+ years building scalable APIs, microservices, and cloud infrastructure. Passionate about system design and performance optimization.",
  
  // 🔗 Social Links - ADD YOUR LINKS (remove unused ones)
  social: {
    github: "https://github.com/yourname",
    linkedin: "https://linkedin.com/in/yourname",
    twitter: "https://twitter.com/yourname",
    // instagram: "https://instagram.com/yourname",
    // youtube: "https://youtube.com/@yourname",
  },
  
  // 📊 SEO & Marketing - CUSTOMIZE KEYWORDS
  seo: {
    title: "Your Name | Senior Backend Engineer",
    description: "Senior Backend Engineer specializing in scalable systems, API architecture, and cloud infrastructure.",
    keywords: [
      "backend developer",
      "software engineer", 
      "API development",
      "microservices",
      "system architecture",
      "cloud computing",
      "Node.js",
      "Python",
      "PostgreSQL",
      "AWS"
    ],
    ogImage: "/images/og-image.jpg",
  },
  
  // ⚙️ Site Features - ENABLE/DISABLE FEATURES
  features: {
    darkMode: true,
    analytics: false,        // Set to true when you add analytics
    contactForm: true,
    blog: false,            // Set to true if you want a blog
  },
  
  // 🐙 GitHub Integration - FOR PORTFOLIO PROJECTS
  github: {
    username: "alexchen",
  },

  // 🎯 Professional Specialization - CHOOSE YOUR PROFESSION
  profession: "developer" as const, // Options: 'developer', 'designer', 'marketer', 'consultant', 'teacher'
  
  // 🎨 UI Labels - CUSTOMIZE BUTTON/MENU TEXT
  labels: {
    navigation: {
      home: "Home",
      about: "About", 
      portfolio: "Projects",    // Backend devs often prefer "Projects"
      resume: "Experience",
      resources: "Resources",
      contact: "Contact"
    },
    buttons: {
      viewPortfolio: "View Projects",
      getInTouch: "Get In Touch",
      downloadResume: "View Experience",
      startConversation: "Let's Talk",
    },
    hero: {
      greeting: "👋 Hello, I'm"
    }
  }
};

// Remove this - profession is now in siteConfig

// 🔧 Helper Functions
export const getSocialLinks = () => Object.entries(siteConfig.social).filter(([_, url]) => url);

export const getNavigation = () => [
  { name: siteConfig.labels.navigation.home, href: '/' },
  { name: siteConfig.labels.navigation.about, href: '/about' },
  { name: siteConfig.labels.navigation.portfolio, href: '/portfolio' },
  { name: siteConfig.labels.navigation.resume, href: '/resume' },
  { name: siteConfig.labels.navigation.resources, href: '/resources' },
  { name: siteConfig.labels.navigation.contact, href: '/contact' },
];

export const getSEOConfig = () => siteConfig.seo;

// Export types for TypeScript users
export type SiteConfig = typeof siteConfig;