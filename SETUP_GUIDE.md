# 🚀 Portfolio Setup Guide

This portfolio template is designed for **easy customization** while keeping your personal information private. Follow this guide to get started quickly.

## 📋 Quick Setup (2 minutes)

### 1. **Copy Configuration File**
```bash
cp src/config/site.example.ts src/config/site.ts
```

### 2. **Customize Your Information**
Edit `src/config/site.ts` and update these key sections:

```typescript
export const siteConfig = {
  // 👤 Personal Information - CHANGE THESE
  name: "Your Name",
  email: "<EMAIL>", 
  jobTitle: "Senior Backend Engineer",
  location: "Your City, Country",
  domain: "https://yourdomain.dev",
  
  // 📝 Professional Bio - CUSTOMIZE THIS  
  bio: "Your professional bio here...",
  
  // 🔗 Social Links - ADD YOUR LINKS
  social: {
    github: "https://github.com/yourusername",
    linkedin: "https://linkedin.com/in/yourusername",
    // Add or remove social links as needed
  },
  
  // 📊 SEO Keywords - CUSTOMIZE FOR YOUR SPECIALTY
  seo: {
    keywords: [
      "backend developer",
      "your specialty",
      "your technologies"
    ]
  }
}
```

### 3. **Start Development**
```bash
npm install
npm run dev
```

That's it! Your portfolio is now personalized and ready to use.

## 🔒 Privacy & Security

### ✅ **What's Protected:**
- Your personal `src/config/site.ts` is **gitignored** (won't be published)
- The template `src/config/site.example.ts` contains safe placeholder data
- No hardcoded personal information in components

### 🔄 **For Public Release:**
- Your customized `site.ts` stays private
- Users get the clean `site.example.ts` template
- Perfect for open source portfolio templates

## 🎯 Backend Developer Optimizations

This template is optimized for backend developers with:

### **Professional Keywords:**
- Backend-focused SEO keywords
- API development, microservices, system architecture
- Popular backend technologies (Node.js, Python, Java, etc.)

### **UI Labels:**
- "Projects" instead of "Portfolio" (more professional)
- "Experience" instead of "Resume"
- Backend-friendly terminology

### **Content Structure:**
- Technical project showcase
- System architecture emphasis
- Performance and scalability focus

## 📁 File Structure

```
src/config/
├── site.example.ts          # 📤 Public template (safe to share)
├── site.ts                  # 🔒 Your private config (gitignored) - MAIN CONFIG
├── content.ts               # Auto-generated content (skills, experience, etc.)
└── profession.ts            # Profession templates (developer, designer, etc.)
```

## 🎯 **Simplified Configuration**

**Main config:** Edit `src/config/site.ts` for everything:
- ✅ Personal information (name, email, bio)
- ✅ Social links and contact info  
- ✅ SEO keywords and meta data
- ✅ **Profession selection** (auto-generates skills & content)
- ✅ UI labels and button text

**Auto-generated:** `src/config/content.ts` automatically provides:
- ✅ Backend-focused skills and experience
- ✅ Professional descriptions and CTAs
- ✅ Industry-specific keywords

## 🛠 Advanced Customization

### **Add New Social Links:**
```typescript
social: {
  github: "https://github.com/username",
  linkedin: "https://linkedin.com/in/username",
  twitter: "https://twitter.com/username",
  youtube: "https://youtube.com/@username",     // Add this
  website: "https://yoursite.com",             // Add this
}
```

### **Customize UI Text:**
```typescript
labels: {
  navigation: {
    portfolio: "Projects",      // Change to "Work", "Cases", etc.
    resume: "Experience",       // Change to "CV", "Background", etc.
  },
  buttons: {
    viewPortfolio: "View Projects",  // Customize button text
  }
}
```

### **Change Your Profession:**
```typescript
// Auto-generates skills, experience, and content for your field
profession: "developer",     // Options: 'developer', 'designer', 'marketer', 'consultant', 'teacher'
```

### **Enable Features:**
```typescript
features: {
  analytics: true,            // Enable when you add Google Analytics
  blog: true,                 // Enable if you want a blog section
  contactForm: true,          // Contact form functionality
}
```

## 🚀 Deployment

1. **Build your site:**
   ```bash
   npm run build
   ```

2. **Deploy to your platform:**
   - **Netlify:** Connect your GitHub repo
   - **Vercel:** Import your GitHub project  
   - **GitHub Pages:** Use GitHub Actions

3. **Your private config stays safe:**
   - `site.ts` is gitignored and won't be deployed
   - Only the public template is shared

## ✨ Why This Approach?

### **User-Friendly:**
- ✅ Simple JavaScript object (no complex YAML/JSON)
- ✅ Clear comments and emojis for easy navigation
- ✅ Industry-standard approach used by popular templates

### **Developer-Friendly:**
- ✅ TypeScript support with full IntelliSense
- ✅ No runtime performance impact
- ✅ Same config pattern as Next.js, Gatsby, etc.

### **Privacy-Focused:**
- ✅ Your personal info never gets committed to git
- ✅ Clean separation between template and personal data
- ✅ Professional open source approach

## 🤝 Contributing

When contributing to this template:
1. Only edit `site.example.ts` (the public template)
2. Never commit your personal `site.ts` file
3. Keep examples professional and backend-focused

---

**Need help?** The configuration file has detailed comments for every section. Just follow the `// CHANGE THESE` markers!