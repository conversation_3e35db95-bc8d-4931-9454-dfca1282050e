export interface ProfessionProfile {
  name: string;
  titles: string[];
  skillCategories: {
    primary: string;
    secondary: string;
    tools: string;
    soft: string;
  };
  commonSkills: {
    primary: string[];
    secondary: string[];
    tools: string[];
    soft: string[];
  };
  seoKeywords: string[];
  experienceHighlights: string[];
  heroSubtitle: string;
  aboutDescription: string;
  portfolioDescription: string;
  ctaText: {
    primary: string;
    secondary: string;
  };
  resourcesDescription: string;
  projectTypes: string[];
}

// Profession-specific configurations
export const professionProfiles: Record<string, ProfessionProfile> = {
  developer: {
    name: "Backend Developer",
    titles: [
      "Senior Backend Engineer",
      "Backend Developer", 
      "Software Engineer",
      "API Developer",
      "Systems Engineer",
      "Full Stack Developer"
    ],
    skillCategories: {
      primary: "Backend Technologies",
      secondary: "Databases & Infrastructure",
      tools: "DevOps & Cloud",
      soft: "Engineering Practices"
    },
    commonSkills: {
      primary: ["Node.js", "Python", "Java", "Go", "TypeScript", "C#"],
      secondary: ["PostgreSQL", "MongoDB", "Redis", "Microservices", "API Design"],
      tools: ["Docker", "Kubernetes", "AWS", "Terraform", "Jenkins"],
      soft: ["System Design", "Performance Optimization", "Code Review", "Technical Leadership"]
    },
    seoKeywords: [
      "backend developer", "software engineer", "API development", 
      "microservices", "system architecture", "database design", "cloud computing"
    ],
    experienceHighlights: [
      "Designed APIs serving millions of requests per day",
      "Architected microservices infrastructure reducing latency by 40%",
      "Optimized database performance achieving 10x query improvements",
      "Built CI/CD pipelines reducing deployment time from hours to minutes",
      "Led technical architecture decisions for high-traffic applications"
    ],
    heroSubtitle: "Building scalable backend systems and APIs that power modern applications",
    aboutDescription: "Backend engineer specializing in building robust, scalable systems that handle millions of users. I focus on API design, database optimization, and cloud architecture to create infrastructure that grows with your business.",
    portfolioDescription: "Backend systems and APIs showcasing scalable architecture and engineering excellence",
    ctaText: {
      primary: "View My Architecture",
      secondary: "Let's Build Scalable Systems"
    },
    resourcesDescription: "Backend development resources and system architecture best practices",
    projectTypes: ["RESTful APIs", "Microservices", "Database Systems", "Cloud Infrastructure", "Real-time Systems"]
  },

  designer: {
    name: "UI/UX Designer",
    titles: [
      "UI/UX Designer",
      "Product Designer",
      "Visual Designer",
      "Interaction Designer",
      "Design Lead"
    ],
    skillCategories: {
      primary: "Design & User Experience",
      secondary: "Research & Strategy",
      tools: "Design Tools & Software",
      soft: "Professional Skills"
    },
    commonSkills: {
      primary: ["UI Design", "UX Research", "Prototyping", "User Testing", "Visual Design"],
      secondary: ["Design Systems", "Information Architecture", "Wireframing", "Interaction Design"],
      tools: ["Figma", "Sketch", "Adobe Creative Suite", "Principle", "InVision"],
      soft: ["Creative Thinking", "Client Communication", "Design Critique", "Presentation"]
    },
    seoKeywords: [
      "ui ux designer", "product designer", "visual designer", 
      "user experience", "interface design", "design systems", "prototyping"
    ],
    experienceHighlights: [
      "Redesigned user interfaces increasing conversion rates by 35%",
      "Conducted user research for products used by 50,000+ people",
      "Created design systems adopted across multiple teams",
      "Led accessibility initiatives achieving WCAG 2.1 AA compliance"
    ],
    heroSubtitle: "Creating beautiful, intuitive experiences that users love",
    aboutDescription: "UI/UX designer passionate about crafting user-centered experiences that are both beautiful and functional. I believe great design solves problems and delights users.",
    portfolioDescription: "A collection of design projects that prioritize user experience and visual excellence",
    ctaText: {
      primary: "View My Portfolio",
      secondary: "Let's Create Together"
    },
    resourcesDescription: "Design resources, inspiration, and UX insights",
    projectTypes: ["Mobile Apps", "Web Platforms", "Design Systems", "Branding", "Prototypes"]
  },

  marketer: {
    name: "Digital Marketer",
    titles: [
      "Digital Marketing Specialist",
      "Marketing Manager",
      "Content Marketing Manager",
      "Social Media Manager",
      "Growth Marketer"
    ],
    skillCategories: {
      primary: "Marketing & Strategy",
      secondary: "Analytics & Optimization",
      tools: "Marketing Tools & Platforms",
      soft: "Professional Skills"
    },
    commonSkills: {
      primary: ["Digital Marketing", "Content Strategy", "SEO/SEM", "Social Media", "Email Marketing"],
      secondary: ["Analytics", "A/B Testing", "Conversion Optimization", "Campaign Management"],
      tools: ["Google Analytics", "HubSpot", "Mailchimp", "Hootsuite", "Canva"],
      soft: ["Strategic Thinking", "Creative Writing", "Data Analysis", "Project Management"]
    },
    seoKeywords: [
      "digital marketer", "marketing specialist", "content marketing", 
      "social media", "seo", "digital strategy", "growth marketing"
    ],
    experienceHighlights: [
      "Increased organic traffic by 150% through strategic SEO initiatives",
      "Managed campaigns generating $500K+ in revenue",
      "Built email marketing funnels with 25% conversion rates",
      "Grew social media following by 300% in 12 months"
    ],
    heroSubtitle: "Driving growth through data-driven marketing strategies and compelling content",
    aboutDescription: "Digital marketing specialist passionate about helping brands connect with their audience and achieve measurable growth through strategic campaigns and content.",
    portfolioDescription: "Successful marketing campaigns and strategies that drive results",
    ctaText: {
      primary: "View My Campaigns",
      secondary: "Let's Grow Together"
    },
    resourcesDescription: "Marketing insights, strategies, and industry trends",
    projectTypes: ["Campaigns", "Content Strategy", "Brand Development", "Analytics", "Growth Initiatives"]
  },

  consultant: {
    name: "Business Consultant",
    titles: [
      "Business Consultant",
      "Management Consultant",
      "Strategy Consultant",
      "Operations Consultant",
      "Digital Transformation Consultant"
    ],
    skillCategories: {
      primary: "Strategy & Analysis",
      secondary: "Process & Operations",
      tools: "Business Tools & Methodologies",
      soft: "Professional Skills"
    },
    commonSkills: {
      primary: ["Strategic Planning", "Business Analysis", "Process Improvement", "Project Management", "Change Management"],
      secondary: ["Data Analysis", "Financial Modeling", "Market Research", "Risk Assessment"],
      tools: ["Excel", "PowerBI", "Salesforce", "Tableau", "Microsoft Project"],
      soft: ["Problem Solving", "Client Relations", "Presentation Skills", "Leadership"]
    },
    seoKeywords: [
      "business consultant", "management consultant", "strategy consultant",
      "business analysis", "process improvement", "digital transformation"
    ],
    experienceHighlights: [
      "Helped clients achieve 30% cost reduction through process optimization",
      "Led digital transformation initiatives for Fortune 500 companies",
      "Developed strategies resulting in $2M+ revenue growth",
      "Managed cross-functional teams of 20+ stakeholders"
    ],
    heroSubtitle: "Transforming businesses through strategic insights and operational excellence",
    aboutDescription: "Business consultant passionate about helping organizations unlock their potential through strategic planning, process improvement, and digital transformation.",
    portfolioDescription: "Case studies of successful business transformations and strategic initiatives",
    ctaText: {
      primary: "View Case Studies",
      secondary: "Let's Transform Together"
    },
    resourcesDescription: "Business insights, strategy frameworks, and industry analysis",
    projectTypes: ["Strategy Development", "Process Optimization", "Digital Transformation", "Analysis", "Training"]
  },

  teacher: {
    name: "Educator",
    titles: [
      "Teacher",
      "Educator",
      "Curriculum Developer",
      "Educational Consultant",
      "Learning Designer"
    ],
    skillCategories: {
      primary: "Teaching & Curriculum",
      secondary: "Learning & Assessment",
      tools: "Educational Technology",
      soft: "Professional Skills"
    },
    commonSkills: {
      primary: ["Curriculum Development", "Lesson Planning", "Classroom Management", "Educational Technology", "Assessment Design"],
      secondary: ["Learning Theory", "Differentiated Instruction", "Student Engagement", "Educational Research"],
      tools: ["Google Classroom", "Canvas", "Zoom", "Kahoot", "Flipgrid"],
      soft: ["Communication", "Patience", "Creativity", "Mentoring"]
    },
    seoKeywords: [
      "teacher", "educator", "curriculum developer", "educational consultant",
      "online learning", "education technology", "instructional design"
    ],
    experienceHighlights: [
      "Improved student test scores by 25% through innovative teaching methods",
      "Developed curriculum used by 500+ students across multiple schools",
      "Integrated technology to enhance learning for diverse student populations",
      "Mentored 15+ new teachers in pedagogical best practices"
    ],
    heroSubtitle: "Inspiring learning and empowering students through innovative education",
    aboutDescription: "Passionate educator dedicated to creating engaging learning experiences that inspire students and foster lifelong learning through innovative teaching methods.",
    portfolioDescription: "Educational projects, curriculum development, and teaching innovations",
    ctaText: {
      primary: "View My Work",
      secondary: "Let's Learn Together"
    },
    resourcesDescription: "Educational resources, teaching strategies, and learning insights",
    projectTypes: ["Curriculum", "Lesson Plans", "Educational Technology", "Research", "Training Materials"]
  }
};

// Helper function to get profession profile
export function getProfessionProfile(profession: string): ProfessionProfile {
  return professionProfiles[profession] || professionProfiles.developer;
}

// Available professions for selection
export const availableProfessions = Object.keys(professionProfiles);