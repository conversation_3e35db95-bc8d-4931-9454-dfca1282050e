import { siteConfig } from './site';
import { getProfessionProfile } from './profession';

export interface SkillCategory {
  name: string;
  skills: string[];
  icon?: string;
}

export interface ExperienceHighlight {
  text: string;
  icon?: string;
}

export interface ContentConfig {
  // Skills & Technologies (automatically populated based on profession)
  skills: {
    primary: string[];
    secondary: string[];
    tools: string[];
    soft: string[];
  };
  
  // Professional Experience Highlights
  experienceHighlights: ExperienceHighlight[];
  
  // Professional Summary
  professionalSummary: string;
  
  // About Page Content
  about: {
    introduction: string;
    expertise: string[];
    interests: string[];
  };
  
  // Resume Content
  resume: {
    summary: string;
    experienceAreas: {
      title: string;
      description: string;
    }[];
  };
  
  // Profession-specific labels
  skillCategories: {
    primary: string;
    secondary: string;
    tools: string;
    soft: string;
  };
}

// Get profession-specific content (auto-generated based on siteConfig.profession)
const professionProfile = getProfessionProfile(siteConfig.profession);

// Dynamic content generation based on profession
export const dynamicContent = {
  // Skills organized by profession
  skills: {
    primary: professionProfile.commonSkills.primary,
    secondary: professionProfile.commonSkills.secondary, 
    tools: professionProfile.commonSkills.tools,
    soft: professionProfile.commonSkills.soft,
  },

  // Skill category labels
  skillCategories: professionProfile.skillCategories,

  // Experience highlights
  experienceHighlights: professionProfile.experienceHighlights.map(text => ({ text })),

  // Professional content
  heroSubtitle: professionProfile.heroSubtitle,
  aboutDescription: professionProfile.aboutDescription,
  portfolioDescription: professionProfile.portfolioDescription,
  professionalSummary: professionProfile.aboutDescription,
  
  // CTA texts
  ctaText: professionProfile.ctaText,
  
  // Project types
  projectTypes: professionProfile.projectTypes,
  
  // Resource description
  resourcesDescription: professionProfile.resourcesDescription,
  
  // Resume experience areas (generated from experience highlights)
  resumeExperienceAreas: professionProfile.experienceHighlights.slice(0, 4).map(text => ({
    title: text.split(' ')[0] + ' ' + text.split(' ')[1], // First two words as title
    description: text
  }))
};

// Content Configuration - Automatically configured based on profession
// You can override any of these values to customize your content
export const contentConfig: ContentConfig = {
  // Skills (automatically populated based on profession)
  skills: dynamicContent.skills,
  
  // Skill category labels (based on profession)
  skillCategories: dynamicContent.skillCategories,
  
  // Professional Experience Highlights (based on profession, customize as needed)
  experienceHighlights: dynamicContent.experienceHighlights,
  
  // Professional Summary (auto-generated, customize as needed)
  professionalSummary: dynamicContent.professionalSummary,
  
  // About Page Content (customize these for your personal touch)
  about: {
    introduction: dynamicContent.aboutDescription,
    expertise: dynamicContent.skills.primary.slice(0, 6), // First 6 primary skills as expertise
    interests: [
      "Continuous learning",
      "Professional development", 
      "Industry trends",
      "Best practices",
      "Knowledge sharing",
      "Innovation"
    ]
  },
  
  // Resume Page Content (auto-generated based on profession)
  resume: {
    summary: dynamicContent.professionalSummary,
    experienceAreas: dynamicContent.resumeExperienceAreas
  }
};

// Utility functions
export function getSkillsByCategory(category: keyof ContentConfig['skills']) {
  return contentConfig.skills[category];
}

export function getAllSkills() {
  return Object.values(contentConfig.skills).flat();
}

export function getExperienceHighlights() {
  return contentConfig.experienceHighlights;
}

export function getProfessionalSummary() {
  return contentConfig.professionalSummary;
}